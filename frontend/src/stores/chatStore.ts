import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { httpChatService, type ConnectionStatus, type TaskStatusData, type MessageData } from '../services/http/chatService'
import type { Message, TaskStatus } from '../types/api'

// 生成唯一ID的工具函数
const generateId = () => `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

// 聊天Store接口
interface ChatStore {
  // 状态
  messages: Message[]
  isConnected: boolean
  connectionStatus: ConnectionStatus
  taskStatus: TaskStatus
  progress: number
  error: string | null
  isProcessing: boolean
  currentTaskId: string | null

  // 操作
  connect: (taskId: string) => Promise<void>
  disconnect: () => void
  sendMessage: (content: string, contextHtml?: string) => Promise<void>
  clearMessages: () => void
  loadTaskMessages: (taskId: string) => Promise<void>
  cancelProcessing: () => Promise<void>
}

// 全局HTTP服务监听器设置（只设置一次）
let isHttpListenersSetup = false

export const useChatStore = create<ChatStore>()(
  devtools(
    immer((set, get) => {
      // 只在第一次创建Store时设置监听器
      if (!isHttpListenersSetup) {
        isHttpListenersSetup = true

        // 设置HTTP服务监听器
        httpChatService.onStatusChange((status: TaskStatusData) => {
          console.log('[DEBUG] ChatStore received status update:', status)
          set((state) => {
            state.taskStatus = status.status
            state.progress = status.progress
            state.isProcessing = status.is_processing
            if (status.error_message) {
              state.error = status.error_message
            }
          })
        })

        httpChatService.onMessageUpdate((messages: MessageData[]) => {
          console.log('[DEBUG] ChatStore received message update:', messages.length, 'messages')
          set((state) => {
            // 转换消息格式
            state.messages = messages.map(msg => ({
              id: msg.id,
              role: msg.role as 'user' | 'assistant',
              content: msg.content,
              timestamp: msg.timestamp,
              has_code_snippet: msg.has_code_snippet
            })).reverse() // 反转顺序，最新的在后面
          })
        })

        httpChatService.onConnectionChange((status: ConnectionStatus) => {
          console.log('[DEBUG] ChatStore received connection change:', status)
          set((state) => {
            state.connectionStatus = status
            state.isConnected = status === 'connected' || status === 'processing'
          })
        })

        httpChatService.onError((error: string) => {
          console.log('[DEBUG] ChatStore received error:', error)
          set((state) => {
            state.error = error
          })
        })
      }

      return {
        // 初始状态
        messages: [],
        isConnected: false,
        connectionStatus: 'idle' as ConnectionStatus,
        taskStatus: 'idle' as TaskStatus,
        progress: 0,
        error: null,
        isProcessing: false,
        currentTaskId: null,

        // 发送消息
        sendMessage: async (content, contextHtml) => {
          try {
            await httpChatService.sendMessage(content, contextHtml)
          } catch (error) {
            console.error('[ChatStore] Failed to send message:', error)
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Failed to send message'
            })
          }
        },

        // 连接到任务
        connect: async (taskId: string) => {
          console.log(`[ChatStore] Connecting to task: ${taskId}`)

          // 清理之前的状态
          set((state) => {
            state.messages = []
            state.error = null
            state.currentTaskId = taskId
          })

          try {
            // 先加载历史消息
            await get().loadTaskMessages(taskId)

            // 连接HTTP服务
            await httpChatService.connect(taskId)

            console.log(`[ChatStore] Successfully connected to task: ${taskId}`)
          } catch (error) {
            console.error(`[ChatStore] Failed to connect to task ${taskId}:`, error)
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Connection failed'
            })
          }
        },

        // 断开连接
        disconnect: () => {
          httpChatService.disconnect()
          set((state) => {
            state.currentTaskId = null
            state.isConnected = false
            state.connectionStatus = 'idle'
          })
        },

        // 加载任务的历史消息
        loadTaskMessages: async (taskId: string) => {
          try {
            console.log(`Loading messages for task: ${taskId}`)
            const messages = await httpChatService.getTaskMessages(taskId)
            console.log(`Loaded ${messages.length} messages for task: ${taskId}`)

            // 消息已经在HTTP服务的监听器中处理了，这里不需要手动设置
          } catch (error) {
            console.error('Failed to load task messages:', error)
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Failed to load messages'
            })
          }
        },

        // 清理聊天数据
        clearMessages: () => {
          set((state) => {
            state.messages = []
            state.error = null
          })
        },

        // 取消处理
        cancelProcessing: async () => {
          const { currentTaskId } = get()
          if (currentTaskId) {
            try {
              await httpChatService.cancelTask(currentTaskId)
            } catch (error) {
              console.error('Failed to cancel task:', error)
              set((state) => {
                state.error = error instanceof Error ? error.message : 'Failed to cancel task'
              })
            }
          }
        }
      }
    }),
    { name: 'ChatStore' }
  )
)

"""
REST API路由
"""
from typing import List
from fastapi import APIRouter, HTTPException, Query
from pydantic import ValidationError

from app.models.schemas import (
    TaskSummary, TaskDetail, Config, ConfigUpdate,
    ReportContentRequest, ReportContentResponse,
    ErrorResponse, MessageRequest, TaskStatusResponse, MessageResponse
)
from app.services.task_service import get_tasks, get_task_detail, delete_task, get_task_status
from app.services.config_service import get_config, update_config
from app.services.report_service import get_report_content, update_report_content, backup_report_content, list_report_backups
from app.services.message_service import get_task_messages
from app.services.async_task_service import async_task_processor


api_router = APIRouter()


@api_router.get("/tasks")
async def get_task_list(
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="分页偏移量")
):
    """获取任务列表"""
    try:
        tasks = await get_tasks(limit=limit, offset=offset)
        
        # 转换为字典格式
        tasks_data = [task.dict() for task in tasks]
        
        return {
            "success": True,
            "data": tasks_data
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get tasks",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/tasks/{task_id}")
async def get_task_details(task_id: str):
    """获取任务详情"""
    try:
        # URL解码任务ID
        import urllib.parse
        decoded_task_id = urllib.parse.unquote(task_id)
        print(f"[DEBUG] Task details request - Original: {task_id}, Decoded: {decoded_task_id}")

        task_detail = await get_task_detail(decoded_task_id)
        
        if not task_detail:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    error="Task not found",
                    code="TASK_NOT_FOUND"
                ).dict()
            )
        
        return {
            "success": True,
            "data": {
                "task": task_detail.task.dict(),
                "messages": [msg.dict() for msg in task_detail.messages]
            }
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get task detail",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.delete("/tasks/{task_id}")
async def delete_task_endpoint(task_id: str):
    """删除任务"""
    try:
        success = await delete_task(task_id)

        if not success:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    error="Task not found",
                    code="TASK_NOT_FOUND"
                ).dict()
            )

        return {
            "success": True,
            "message": "Task deleted successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to delete task",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/tasks/{task_id}/report")
async def get_task_report_content(task_id: str):
    """获取任务报告内容"""
    try:
        report_content = await get_report_content(task_id)

        if not report_content:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    error="Report not found",
                    code="REPORT_NOT_FOUND"
                ).dict()
            )

        return {
            "success": True,
            "data": report_content.dict()
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get report content",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.put("/tasks/{task_id}/report")
async def update_task_report_content(task_id: str, request: ReportContentRequest):
    """更新任务报告内容"""
    try:
        # 先创建备份
        backup_path = await backup_report_content(task_id)

        # 更新报告内容
        updated_content = await update_report_content(task_id, request.content)

        if not updated_content:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    error="Task not found or failed to update report",
                    code="UPDATE_FAILED"
                ).dict()
            )

        return {
            "success": True,
            "data": updated_content.dict(),
            "backup_path": backup_path
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to update report content",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/tasks/{task_id}/report/backups")
async def get_task_report_backups(task_id: str):
    """获取任务报告备份列表"""
    try:
        backups = await list_report_backups(task_id)

        return {
            "success": True,
            "data": {
                "backups": backups,
                "count": len(backups)
            }
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get report backups",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/config")
async def get_system_config():
    """获取系统配置"""
    try:
        config = await get_config()
        
        return {
            "success": True,
            "data": config.dict()
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get configuration",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.put("/config")
async def update_system_config(config_update: ConfigUpdate):
    """更新系统配置"""
    try:
        await update_config(config_update)
        
        return {
            "success": True,
            "message": "Configuration updated successfully"
        }
    
    except ValidationError as e:
        raise HTTPException(
            status_code=400,
            detail=ErrorResponse(
                error="Invalid configuration",
                code="INVALID_CONFIG",
                details={"validation_errors": e.errors()}
            ).dict()
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to update configuration",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.post("/tasks/{task_id}/messages")
async def send_message(task_id: str, request: MessageRequest):
    """发送消息并启动异步处理"""
    try:
        # URL解码任务ID
        import urllib.parse
        decoded_task_id = urllib.parse.unquote(task_id)
        print(f"[DEBUG] Send message request - Original: {task_id}, Decoded: {decoded_task_id}")

        # 启动异步处理
        result = await async_task_processor.process_message(
            decoded_task_id,
            request.content,
            request.context_html or ""
        )

        return {
            "success": True,
            "message": result,
            "task_id": decoded_task_id
        }

    except ValueError as e:
        raise HTTPException(
            status_code=409,
            detail=ErrorResponse(
                error=str(e),
                code="TASK_ALREADY_PROCESSING"
            ).dict()
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to process message",
                code="PROCESSING_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/tasks/{task_id}/status")
async def get_task_status_endpoint(task_id: str):
    """获取任务状态"""
    try:
        # URL解码任务ID（FastAPI会自动解码，但为了确保兼容性）
        import urllib.parse
        decoded_task_id = urllib.parse.unquote(task_id)
        print(f"[DEBUG] Task status request - Original: {task_id}, Decoded: {decoded_task_id}")

        # 获取数据库中的任务状态
        status_data = await get_task_status(decoded_task_id)

        if not status_data:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponse(
                    error="Task not found",
                    code="TASK_NOT_FOUND"
                ).dict()
            )

        # 获取处理器状态
        processor_status = async_task_processor.get_task_status(decoded_task_id)

        # 获取消息数量
        messages = await get_task_messages(decoded_task_id)

        return {
            "success": True,
            "data": {
                "id": status_data["id"],
                "status": status_data["status"],
                "progress": status_data["progress"],
                "error_message": status_data["error_message"],
                "last_activity": status_data["last_activity"],
                "is_processing": processor_status["is_running"],
                "message_count": len(messages)
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get task status",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.get("/tasks/{task_id}/messages")
async def get_task_messages_endpoint(task_id: str, limit: int = Query(50, ge=1, le=100)):
    """获取任务消息列表"""
    try:
        # URL解码任务ID
        import urllib.parse
        decoded_task_id = urllib.parse.unquote(task_id)
        print(f"[DEBUG] Get messages request - Original: {task_id}, Decoded: {decoded_task_id}")

        messages = await get_task_messages(decoded_task_id, limit)

        return {
            "success": True,
            "data": [
                {
                    "id": msg.id,
                    "role": msg.role,
                    "content": msg.content,
                    "timestamp": msg.timestamp,
                    "has_code_snippet": msg.has_code_snippet
                }
                for msg in messages
            ]
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to get messages",
                code="DATABASE_ERROR",
                details={"message": str(e)}
            ).dict()
        )


@api_router.post("/tasks/{task_id}/cancel")
async def cancel_task_processing(task_id: str):
    """取消任务处理"""
    try:
        # URL解码任务ID
        import urllib.parse
        decoded_task_id = urllib.parse.unquote(task_id)
        print(f"[DEBUG] Cancel task request - Original: {task_id}, Decoded: {decoded_task_id}")

        cancelled = await async_task_processor.cancel_task(decoded_task_id)

        return {
            "success": True,
            "cancelled": cancelled,
            "message": "Task cancelled" if cancelled else "Task was not running"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=ErrorResponse(
                error="Failed to cancel task",
                code="PROCESSING_ERROR",
                details={"message": str(e)}
            ).dict()
        )

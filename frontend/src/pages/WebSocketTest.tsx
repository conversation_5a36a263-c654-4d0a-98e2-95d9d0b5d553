import React, { useState, useEffect } from 'react'
import { useChatStore, useTaskStore, useContextStore } from '../stores'

const WebSocketTest: React.FC = () => {
  const { currentTask, selectTask } = useTaskStore()
  const { 
    messages, 
    isConnected, 
    connectionStatus, 
    sendMessage, 
    connect,
    clearMessages 
  } = useChatStore()
  const { addContext, getCombinedHtmlContent, clearContext, items } = useContextStore()
  
  const [testMessage, setTestMessage] = useState('')
  const [testHtml, setTestHtml] = useState('<div><h1>测试标题</h1><p>测试内容</p></div>')

  // 测试任务ID
  const testTaskId = 'test_task_websocket_debug'

  useEffect(() => {
    // 自动选择测试任务
    selectTask(testTaskId)
  }, [selectTask])

  const handleAddTestContext = () => {
    addContext({
      type: 'html_content',
      content: testHtml,
      textContent: '测试标题 测试内容',
      summary: '测试HTML内容',
      source: 'manual'
    })
  }

  const handleSendTestMessage = () => {
    if (testMessage.trim()) {
      sendMessage(testMessage, getCombinedHtmlContent())
      setTestMessage('')
    }
  }

  const handleSendWithoutContext = () => {
    if (testMessage.trim()) {
      sendMessage(testMessage, '')
      setTestMessage('')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">WebSocket 调试测试</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：控制面板 */}
          <div className="space-y-6">
            {/* 连接状态 */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">连接状态</h2>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">任务ID:</span>
                  <span className="text-sm text-gray-600">{currentTask?.id || '未选择'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">连接状态:</span>
                  <span className={`text-sm px-2 py-1 rounded ${
                    isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {connectionStatus}
                  </span>
                </div>
              </div>
            </div>

            {/* 上下文管理 */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">上下文管理</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    测试HTML内容
                  </label>
                  <textarea
                    value={testHtml}
                    onChange={(e) => setTestHtml(e.target.value)}
                    className="w-full h-24 px-3 py-2 border border-gray-300 rounded-md text-sm"
                    placeholder="输入HTML内容..."
                  />
                </div>
                
                <div className="flex space-x-2">
                  <button
                    onClick={handleAddTestContext}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                  >
                    添加到上下文
                  </button>
                  <button
                    onClick={clearContext}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm"
                  >
                    清空上下文
                  </button>
                </div>
                
                <div className="text-sm text-gray-600">
                  当前上下文项数: {items.length}
                  <br />
                  合并HTML长度: {getCombinedHtmlContent().length}
                </div>
              </div>
            </div>

            {/* 消息发送 */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">消息发送</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    测试消息
                  </label>
                  <textarea
                    value={testMessage}
                    onChange={(e) => setTestMessage(e.target.value)}
                    className="w-full h-20 px-3 py-2 border border-gray-300 rounded-md text-sm"
                    placeholder="输入测试消息..."
                  />
                </div>
                
                <div className="flex space-x-2">
                  <button
                    onClick={handleSendTestMessage}
                    disabled={!isConnected || !testMessage.trim()}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 text-sm"
                  >
                    发送（带上下文）
                  </button>
                  <button
                    onClick={handleSendWithoutContext}
                    disabled={!isConnected || !testMessage.trim()}
                    className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 disabled:bg-gray-400 text-sm"
                  >
                    发送（无上下文）
                  </button>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">操作</h2>
              <div className="flex space-x-2">
                <button
                  onClick={() => connect(testTaskId)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                >
                  重新连接
                </button>
                <button
                  onClick={clearMessages}
                  className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm"
                >
                  清空消息
                </button>
              </div>
            </div>
          </div>

          {/* 右侧：消息列表 */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">消息历史</h2>
            
            <div className="h-96 overflow-y-auto border border-gray-200 rounded-md p-4 space-y-4">
              {messages.length === 0 ? (
                <div className="text-gray-500 text-center">暂无消息</div>
              ) : (
                messages.map((message) => (
                  <div
                    key={message.id}
                    className={`p-3 rounded-md ${
                      message.role === 'user' 
                        ? 'bg-blue-50 border-l-4 border-blue-400' 
                        : 'bg-gray-50 border-l-4 border-gray-400'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">
                        {message.role === 'user' ? '用户' : '助手'}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="text-sm text-gray-700 whitespace-pre-wrap">
                      {message.content}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* 调试信息 */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">调试信息</h2>
          <div className="text-sm text-gray-600 space-y-2">
            <div>当前任务: {currentTask?.id}</div>
            <div>连接状态: {connectionStatus}</div>
            <div>消息数量: {messages.length}</div>
            <div>上下文项数: {items.length}</div>
            <div>合并HTML内容长度: {getCombinedHtmlContent().length}</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default WebSocketTest

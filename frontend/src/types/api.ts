// API响应基础类型
export interface APIResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  code?: string
}

// 任务相关类型
export interface Task {
  id: string
  title: string
  created_at: string
  updated_at: string
  message_count: number
  report_file_path?: string | null
  report_html_filename?: string
}

export interface TaskDetail {
  task: Task
  messages: Message[]
}

export interface TaskQueryParams {
  limit?: number
  offset?: number
  search?: string
  dateRange?: [string, string]
}

// 消息相关类型
export interface Message {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  has_code_snippet: boolean
  task_id?: string
}

// 任务状态类型
export type TaskStatus = 'idle' | 'processing' | 'completed' | 'error'

// 系统配置类型
export interface SystemConfig {
  llm_base_url: string
  llm_api_key: string
  llm_model: string
  report_storage_path: string
  debug_mode: boolean
}

// WebSocket消息类型
export interface UserMessage {
  type: 'user_message'
  content: string
  context_html?: string
}

export type WebSocketMessage = 
  | AssistantMessageChunk
  | CodeSnippetStart
  | CodeSnippetChunk
  | CodeSnippetEnd
  | ErrorMessage

export interface AssistantMessageChunk {
  type: 'assistant_message_chunk'
  content: string
  is_code_snippet: boolean
}

export interface CodeSnippetStart {
  type: 'code_snippet_start'
  content: ''
}

export interface CodeSnippetChunk {
  type: 'code_snippet_chunk'
  content: string
}

export interface CodeSnippetEnd {
  type: 'code_snippet_end'
  content: ''
  report_file_path: string
}

export interface ErrorMessage {
  type: 'error'
  content: string
}

// API错误类型
export class APIError extends Error {
  constructor(
    public status: number,
    public statusText: string,
    public code?: string,
    public details?: any
  ) {
    super(`API Error: ${status} ${statusText}`)
    this.name = 'APIError'
  }
}

export class NetworkError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'NetworkError'
  }
}

export class TimeoutError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'TimeoutError'
  }
}

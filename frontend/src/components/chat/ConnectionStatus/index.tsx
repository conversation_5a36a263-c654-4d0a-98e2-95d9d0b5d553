import React from 'react'
import clsx from 'clsx'
import type { ConnectionStatus as ConnectionStatusType } from '../../../services/http/chatService'

interface ConnectionStatusProps {
  isConnected: boolean
  connectionStatus: ConnectionStatusType
  taskId?: string
  className?: string
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  isConnected,
  connectionStatus,
  taskId,
  className = ''
}) => {
  // 只在非正常连接状态时显示
  if (isConnected && connectionStatus === 'connected') {
    return null
  }

  const getStatusConfig = () => {
    switch (connectionStatus) {
      case 'connecting':
        return {
          color: 'bg-yellow-50 border-yellow-200 text-yellow-800',
          icon: (
            <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          ),
          title: '正在连接...',
          message: '正在建立与服务器的连接，请稍候'
        }
      case 'processing':
        return {
          color: 'bg-blue-50 border-blue-200 text-blue-800',
          icon: (
            <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          ),
          title: '正在处理...',
          message: '正在生成报告，请稍候'
        }
      case 'idle':
        return {
          color: 'bg-gray-50 border-gray-200 text-gray-800',
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M18.364 5.636l-12.728 12.728m0-12.728l12.728 12.728"
              />
            </svg>
          ),
          title: '未连接',
          message: taskId ? '请选择一个任务以建立连接' : '未连接到服务器'
        }
      case 'error':
        return {
          color: 'bg-red-50 border-red-200 text-red-800',
          icon: (
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          ),
          title: '连接错误',
          message: '无法连接到服务器，请检查网络连接'
        }
      default:
        return null
    }
  }

  const statusConfig = getStatusConfig()
  if (!statusConfig) return null

  return (
    <div className={clsx('mx-4 mt-2', className)}>
      <div className={clsx('rounded-lg border p-3', statusConfig.color)}>
        <div className="flex items-center space-x-2">
          {statusConfig.icon}
          <div>
            <div className="font-medium text-sm">{statusConfig.title}</div>
            <div className="text-xs opacity-75">{statusConfig.message}</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ConnectionStatus

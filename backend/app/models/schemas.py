"""
Pydantic数据模型定义
"""
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field
from enum import Enum


class TaskStatus(str, Enum):
    """任务状态枚举"""
    IDLE = "idle"
    PROCESSING = "processing"
    COMPLETED = "completed"
    ERROR = "error"


class MessageBase(BaseModel):
    """消息基础模型"""
    role: str = Field(..., pattern="^(user|assistant)$")
    content: str
    has_code_snippet: bool = False


class MessageCreate(MessageBase):
    """创建消息模型"""
    task_id: str


class Message(MessageBase):
    """消息模型"""
    id: str
    task_id: str
    timestamp: datetime
    
    class Config:
        from_attributes = True


class TaskBase(BaseModel):
    """任务基础模型"""
    title: str


class TaskCreate(TaskBase):
    """创建任务模型"""
    pass


class Task(TaskBase):
    """任务模型"""
    id: str
    created_at: datetime
    updated_at: datetime
    report_file_path: Optional[str] = None
    report_html_filename: str = "report.html"
    status: TaskStatus = TaskStatus.IDLE
    progress: int = 0
    error_message: Optional[str] = None
    last_activity: datetime

    class Config:
        from_attributes = True


class TaskSummary(BaseModel):
    """任务摘要模型"""
    id: str
    title: str
    created_at: datetime
    updated_at: datetime
    message_count: int


class TaskDetail(BaseModel):
    """任务详情模型"""
    task: Task
    messages: List[Message]


class ConfigBase(BaseModel):
    """配置基础模型"""
    llm_base_url: str = Field(..., pattern=r"^https?://.*")
    llm_api_key: str = Field(..., min_length=1)
    llm_model: str = Field(..., min_length=1)
    report_storage_path: str = Field(..., min_length=1)
    debug_mode: bool = False


class Config(ConfigBase):
    """配置模型（用于返回，API Key已脱敏）"""
    pass


class ConfigUpdate(ConfigBase):
    """配置更新模型"""
    pass


class WebSocketMessage(BaseModel):
    """WebSocket消息模型"""
    type: str
    content: str
    context_html: Optional[str] = None


class APIResponse(BaseModel):
    """API响应基础模型"""
    success: bool
    data: Optional[dict] = None
    message: Optional[str] = None


class ReportContentRequest(BaseModel):
    """报告内容更新请求模型"""
    content: str = Field(..., description="更新后的HTML内容")


class ReportContentResponse(BaseModel):
    """报告内容响应模型"""
    content: str = Field(..., description="报告HTML内容")
    file_path: str = Field(..., description="报告文件路径")
    last_modified: datetime = Field(..., description="最后修改时间")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = False
    error: str
    code: str
    details: Optional[dict] = None


class MessageRequest(BaseModel):
    """发送消息请求模型"""
    content: str = Field(..., description="消息内容")
    context_html: Optional[str] = Field(None, description="上下文HTML内容")


class TaskStatusResponse(BaseModel):
    """任务状态响应模型"""
    id: str
    status: TaskStatus
    progress: int
    error_message: Optional[str] = None
    last_activity: datetime
    has_new_messages: bool = False
    message_count: int = 0


class MessageResponse(BaseModel):
    """消息响应模型"""
    id: str
    role: str
    content: str
    timestamp: datetime
    has_code_snippet: bool = False

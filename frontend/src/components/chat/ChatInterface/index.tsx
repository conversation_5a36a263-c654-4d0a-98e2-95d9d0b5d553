import React, { useEffect } from 'react'
import clsx from 'clsx'
import { useChatStore, useTaskStore, useContextStore } from '../../../stores'
import ChatHeader from '../ChatHeader'
import MessageList from '../MessageList'
import ChatInput from '../ChatInput'
import ConnectionStatus from '../ConnectionStatus'

interface ChatInterfaceProps {
  className?: string
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  className = ''
}) => {
  const { currentTask } = useTaskStore()
  const { getCombinedHtmlContent } = useContextStore()
  const {
    messages,
    isConnected,
    connectionStatus,
    isProcessing,
    connect,
    sendMessage,
    clearMessages
  } = useChatStore()

  // 当选择任务时自动连接WebSocket
  useEffect(() => {
    if (currentTask?.id) {
      connect(currentTask.id)
    } else {
      // 如果没有选择任务，清理聊天数据
      clearMessages()
    }
  }, [currentTask?.id, connect, clearMessages])

  const handleSendMessage = (content: string, contextHtml?: string) => {
    if (content.trim()) {
      // 如果没有提供contextHtml，尝试从上下文存储中获取
      const finalContextHtml = contextHtml || getCombinedHtmlContent()
      console.log('[DEBUG] ChatInterface: Sending message with context:', {
        content: content.substring(0, 50) + '...',
        hasProvidedContext: !!contextHtml,
        hasStoredContext: !!getCombinedHtmlContent(),
        finalContextLength: finalContextHtml.length
      })
      sendMessage(content, finalContextHtml)
    }
  }

  const handleContextClear = () => {
    // 上下文清理功能暂时移除
  }

  return (
    <div
      className={clsx('flex flex-col h-full bg-white overflow-hidden', className)}
      style={{
        minWidth: '400px'
      }}
    >
      {/* 聊天头部 */}
      <ChatHeader
        taskTitle={currentTask?.title || '未选择任务'}
        isConnected={isConnected}
        connectionStatus={connectionStatus}
      />

      {/* 连接状态提示 */}
      <ConnectionStatus
        isConnected={isConnected}
        connectionStatus={connectionStatus}
        taskId={currentTask?.id}
      />

      {/* 消息列表 */}
      <div className="flex-1 min-h-0 overflow-hidden">
        <MessageList
          messages={messages}
          loading={connectionStatus === 'connecting'}
          isGeneratingReport={isProcessing}
        />
      </div>

      {/* 输入区域 */}
      <div className="border-t border-gray-200">
        <ChatInput
          onSend={handleSendMessage}
          disabled={!isConnected || !currentTask}
          placeholder={
            !currentTask
              ? '请先选择一个任务...'
              : !isConnected
              ? '连接中...'
              : '输入消息...'
          }
          onContextClear={handleContextClear}
        />
      </div>
    </div>
  )
}

export default ChatInterface
